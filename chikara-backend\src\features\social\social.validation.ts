import { z } from "zod";

// Friend request validation
const sendFriendRequestSchema = z.object({
    userId: z.number().int().positive(),
});

const respondToFriendRequestSchema = z.object({
    requestId: z.number().int().positive(),
    accept: z.boolean(),
});

const removeFriendSchema = z.object({
    friendId: z.number().int().positive(),
});

// Path parameter validation schemas
const friendIdParamSchema = z.object({
    friendId: z
        .string()
        .regex(/^\d+$/)
        .transform(Number)
        .refine((n) => n > 0, {
            message: "Friend ID must be a positive integer",
        }),
});

const rivalIdParamSchema = z.object({
    rivalId: z
        .string()
        .regex(/^\d+$/)
        .transform(Number)
        .refine((n) => n > 0, {
            message: "Rival ID must be a positive integer",
        }),
});

const searchUsersSchema = z.object({
    query: z.string().min(1).max(50),
    limit: z.number().int().min(1).max(50).optional().default(10),
});

const updateStatusMessageSchema = z.object({
    message: z.string().max(100).nullable().optional(),
});

// Rival validation
const addRivalSchema = z.object({
    userId: z.number().int().positive(),
});

const removeRivalSchema = z.object({
    rivalId: z.number().int().positive(),
});

const updateRivalNoteSchema = z.object({
    rivalId: z.number().int().positive(),
    note: z.string().max(255).nullable().optional(),
});

// Friend note validation
const updateFriendNoteSchema = z.object({
    friendId: z.number().int().positive(),
    note: z.string().max(255).nullable().optional(),
});

const togglePrivacySettingSchema = z.object({
    showLastOnline: z.boolean().optional(),
});

// For ORPC, we need to export individual schemas and a combined object
export const socialSchema = {
    sendFriendRequest: sendFriendRequestSchema,
    respondToFriendRequest: respondToFriendRequestSchema,
    removeFriend: z.object({
        friendId: z.number().int().positive(),
    }),
    updateFriendNote: updateFriendNoteSchema,
    updateStatusMessage: updateStatusMessageSchema,
    togglePrivacySetting: togglePrivacySettingSchema,
    addRival: addRivalSchema,
    removeRival: z.object({
        rivalId: z.number().int().positive(),
    }),
    updateRivalNote: updateRivalNoteSchema,
};

const socialValidation = {
    sendFriendRequestSchema,
    respondToFriendRequestSchema,
    removeFriendSchema,
    searchUsersSchema,
    updateStatusMessageSchema,
    addRivalSchema,
    removeRivalSchema,
    updateRivalNoteSchema,
    updateFriendNoteSchema,
    togglePrivacySettingSchema,
    friendIdParamSchema,
    rivalIdParamSchema,
};

export default socialValidation;
