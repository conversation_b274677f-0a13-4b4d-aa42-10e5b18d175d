import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
// import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { Eye, EyeOff, Pencil, X } from "lucide-react";
import { useState } from "react";
import useTogglePrivacySettings from "../api/useTogglePrivacySettings";
import useUpdateStatusMessage from "../api/useUpdateStatusMessage";

interface StatusMessageSettingProps {
    statusMessage: string | null;
    statusMessageUpdatedAt: Date | null;
    showOnlineStatus?: boolean;
}

export default function StatusMessageSetting({
    statusMessage,
    statusMessageUpdatedAt,
    showOnlineStatus = true,
}: StatusMessageSettingProps) {
    const [isEditing, setIsEditing] = useState(false);
    const [message, setMessage] = useState(statusMessage || "");

    const { mutate: updateStatusMessage, isPending } = useUpdateStatusMessage();
    const { mutate: toggleOnlineStatus, isPending: isTogglePending } = useTogglePrivacySettings();

    const handleSave = () => {
        updateStatusMessage({ message: message.trim() || null });
        setIsEditing(false);
    };

    const handleClear = () => {
        updateStatusMessage({ message: null });
        setMessage("");
        setIsEditing(false);
    };

    const handleToggleOnlineStatus = () => {
        toggleOnlineStatus({ showLastOnline: !showOnlineStatus });
    };

    const lastUpdated = statusMessageUpdatedAt
        ? formatDistanceToNow(new Date(statusMessageUpdatedAt), { addSuffix: true })
        : null;

    return (
        <div className="bg-gray-800 rounded-lg border border-gray-700">
            <div className="px-3 pt-3">
                <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium font-display text-indigo-500">Status Message</h3>

                    {!isEditing && (
                        <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-blue-400 hover:text-blue-300 rounded-lg"
                            onClick={() => setIsEditing(true)}
                        >
                            <Pencil className="size-3.5 mr-1" />
                            <span>Edit</span>
                        </Button>
                    )}
                </div>
                {!statusMessage && (
                    <p className="text-gray-300 italic text-xs mb-3">Set a status message for your friends to see.</p>
                )}
            </div>
            {isEditing ? (
                <div className="space-y-2 px-3 mb-4 mt-3">
                    <Input
                        placeholder="Set your status message..."
                        value={message}
                        maxLength={100}
                        className="bg-gray-900 border-gray-700 text-gray-200"
                        onChange={(e) => setMessage(e.target.value)}
                    />
                    <div className="flex justify-between text-xs text-gray-400">
                        <span>{message.length}/100</span>
                        {lastUpdated && <span>Last updated {lastUpdated}</span>}
                    </div>
                    <div className="flex gap-2 justify-end mx-4">
                        <Button
                            variant="destructive"
                            size="sm"
                            disabled={isPending}
                            className={cn("h-8 rounded-lg", (!statusMessage || statusMessage === "") && "hidden")}
                            onClick={handleClear}
                        >
                            <X className="size-3.5 mr-1" />
                            <span>Clear</span>
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            disabled={isPending}
                            className="h-8 rounded-lg text-white"
                            onClick={() => setIsEditing(false)}
                        >
                            Cancel
                        </Button>
                        <Button
                            variant="default"
                            size="sm"
                            disabled={isPending || message === statusMessage}
                            className="h-8 rounded-lg w-24"
                            onClick={handleSave}
                        >
                            Save
                        </Button>
                    </div>
                </div>
            ) : (
                <div>
                    {statusMessage ? (
                        <div className="px-3 pb-3">
                            <p className="text-gray-300 break-words">{statusMessage}</p>
                            {lastUpdated && <p className="text-xs text-gray-400 mt-1">Updated {lastUpdated}</p>}
                        </div>
                    ) : null}
                </div>
            )}

            {/* Online Status Privacy Toggle */}
            {/* <div className="border-t border-gray-700 p-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                        {showOnlineStatus ? (
                            <Eye className="size-4 text-green-500" />
                        ) : (
                            <EyeOff className="size-4 text-gray-400" />
                        )}
                        <div>
                            <h4 className="text-sm font-medium text-gray-200">Show Online Status</h4>
                            <p className="text-xs text-gray-400">
                                {showOnlineStatus
                                    ? "Friends can see when you're online"
                                    : "Your online status is hidden from friends"}
                            </p>
                        </div>
                    </div>
                    <Switch
                        checked={showOnlineStatus}
                        disabled={isTogglePending}
                        onCheckedChange={handleToggleOnlineStatus}
                    />
                </div>
            </div> */}
        </div>
    );
}
